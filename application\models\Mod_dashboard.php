<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Create BY : Aryo
 * Youtube : Aryo Coding
 */
class Mod_dashboard extends CI_Model
{

	function __construct()
	{
		parent::__construct();
		$this->load->database();
	}
	
	function get_akses_menu($link, $level)
	{

		$this->db->where('a.id_level', $level);
		$this->db->where('b.link', $link);
		$this->db->join('tbl_menu b', 'b.id_menu=a.id_menu');
		return $this->db->get('tbl_akses_menu a');
	}

	function get_akses_submenu($link, $level)
	{

		$this->db->where('a.id_level', $level);
		$this->db->where('b.link', $link);
		$this->db->join('tbl_submenu b', 'b.id_submenu=a.id_submenu');
		return $this->db->get('tbl_akses_submenu a');
	}

	function JmlUser()
	{
		$this->db->from('tbl_user');
		return $this->db->count_all_results();
	}

	function Jmlbarang()
	{
		$this->db->from('barang');
		return $this->db->count_all_results();
	}

	function get_low_stock_items($limit = 5)
	{
		// Pastikan field dan tabel sesuai: barang (id_barang, nama_barang, stok, id_satuan, aktif), satuan (id_satuan, nama_satuan)
		$this->db->select('b.id_barang, b.nama_barang, b.stok, b.stok_minimum, s.nama_satuan');
		$this->db->from('barang b');
		$this->db->join('satuan s', 'b.id_satuan = s.id_satuan', 'left');
		$this->db->where('b.aktif', 1);
		$this->db->where('b.stok <=', 10);
		$this->db->order_by('b.stok', 'ASC');
		$this->db->limit($limit);
		return $this->db->get()->result();
	}

	function get_recent_transactions($limit = 5)
	{
		// Pastikan field dan tabel sesuai: penerimaan (id_penerimaan, id_supplier, tanggal, status), supplier (id_supplier, nama_supplier)
		$this->db->select('p.id_penerimaan, p.tanggal, p.status, s.nama_supplier, 
						  (SELECT SUM(d.harga * d.jumlah) FROM penerimaan_detail d WHERE d.id_penerimaan = p.id_penerimaan) as total');
		$this->db->from('penerimaan p');
		$this->db->join('supplier s', 'p.id_supplier = s.id_supplier', 'left');
		$this->db->order_by('p.tanggal', 'DESC');
		$this->db->limit($limit);
		return $this->db->get()->result();
	}

	function count_suppliers()
	{
		// Pastikan field dan tabel sesuai: supplier (id_supplier, status_aktif)
		$this->db->from('supplier');
		$this->db->where('status_aktif', 1);
		return $this->db->count_all_results();
	}
	
	function total_stock_value()
	{
		// Pastikan field dan tabel sesuai: barang (stok, harga_jual, aktif)
		$this->db->select('SUM(stok * harga_jual) as total_value');
		$this->db->from('barang');
		$this->db->where('aktif', 1);
		$result = $this->db->get()->row();
		return $result ? $result->total_value : 0;
	}

	function get_monthly_transactions()
	{
		$this->db->select("DATE_FORMAT(tanggal, '%Y-%m') as month, COUNT(*) as count");
		$this->db->from('penerimaan');
		$this->db->where('YEAR(tanggal)', date('Y'));
		$this->db->group_by("DATE_FORMAT(tanggal, '%Y-%m')");
		$this->db->order_by("DATE_FORMAT(tanggal, '%Y-%m')");
		return $this->db->get()->result();
	}

	function get_category_distribution()
	{
		$this->db->select('k.nama_kategori, COUNT(b.id_barang) as count');
		$this->db->from('barang b');
		$this->db->join('kategori k', 'b.id_kategori = k.id_kategori', 'left');
		$this->db->where('b.aktif', 1);
		$this->db->group_by('k.nama_kategori');
		return $this->db->get()->result();
	}
}
