<section class="content">
  <div class="container-fluid">
    <!-- Summary Stats -->
    <div class="row">
      <div class="col-lg-3 col-6">
        <div class="small-box bg-info">
          <div class="inner">
            <h3><?php echo $jmlbarang; ?></h3>
            <p>Total Barang</p>
          </div>
          <div class="icon">
            <i class="fas fa-boxes"></i>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-6">
        <div class="small-box bg-success">
          <div class="inner">
            <h3>Rp <?php echo number_format($total_stock_value, 0, ',', '.'); ?></h3>
            <p>Total Nilai Stok</p>
          </div>
          <div class="icon">
            <i class="fas fa-money-bill-wave"></i>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-6">
        <div class="small-box bg-warning">
          <div class="inner">
            <h3><?php echo $total_suppliers; ?></h3>
            <p>Total Supplier</p>
          </div>
          <div class="icon">
            <i class="fas fa-truck"></i>
          </div>
        </div>
      </div>
      
      <div class="col-lg-3 col-6">
        <div class="small-box bg-danger">
          <div class="inner">
            <h3><?php echo count($low_stock); ?></h3>
            <p>Barang Stok Menipis</p>
          </div>
          <div class="icon">
            <i class="fas fa-exclamation-triangle"></i>
          </div>
          <a href="#low-stock-items" class="small-box-footer">
            Lihat Detail <i class="fas fa-arrow-circle-right"></i>
          </a>
        </div>
      </div>
    </div>

    <!-- Charts Row -->
    <div class="row">
      <div class="col-md-8">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-line mr-1"></i>
              Tren Transaksi Bulanan
            </h3>
          </div>
          <div class="card-body">
            <canvas id="transactions-chart" height="300"></canvas>
          </div>
        </div>
      </div>
      
      <div class="col-md-4">
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-chart-pie mr-1"></i>
              Distribusi Kategori Barang
            </h3>
          </div>
          <div class="card-body">
            <canvas id="category-chart" height="300"></canvas>
          </div>
        </div>
      </div>
    </div>

    <!-- Main row -->
    <div class="row">
      <!-- Left col -->
      <section class="col-lg-7 connectedSortable">
        <!-- Recent Transactions -->
        <div class="card" id="recent-transactions">
          <div class="card-header border-transparent">
            <h3 class="card-title">
              <i class="fas fa-shopping-cart mr-1"></i>
              Transaksi Terbaru
            </h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          <!-- /.card-header -->
          <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
              <thead>
                <tr>
                  <th>Tanggal</th>
                  <th>Supplier</th>
                  <th>Status</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach($recent_transactions as $trans): ?>
                <tr>
                  <td><?php echo date('d M Y', strtotime($trans->tanggal)); ?></td>
                  <td><?php echo $trans->nama_supplier; ?></td>
                  <td>
                    <?php if($trans->status == 1): ?>
                      <span class="badge badge-success">Selesai</span>
                    <?php else: ?>
                      <span class="badge badge-warning">Pending</span>
                    <?php endif; ?>
                  </td>
                  <td class="text-right">
                    <?php echo isset($trans->total) ? 'Rp '.number_format($trans->total, 0, ',', '.') : '-'; ?>
                  </td>
                </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
          <!-- /.card-body -->
          <div class="card-footer clearfix">
            <a href="<?php echo site_url('penerimaan'); ?>" class="btn btn-sm btn-info float-right">Lihat Semua Transaksi</a>
          </div>
          <!-- /.card-footer -->
        </div>
        <!-- /.card -->
      </section>
      <!-- /.Left col -->

      <!-- right col (We are only adding the ID to make the widgets sortable)-->
      <section class="col-lg-5 connectedSortable">
        <!-- Low Stock Items box -->
        <div class="card" id="low-stock-items">
          <div class="card-header border-transparent">
            <h3 class="card-title">
              <i class="fas fa-exclamation-triangle mr-1"></i>
              Barang Stok Menipis
            </h3>
            <div class="card-tools">
              <button type="button" class="btn btn-tool" data-card-widget="collapse">
                <i class="fas fa-minus"></i>
              </button>
            </div>
          </div>
          <!-- /.card-header -->
          <div class="card-body table-responsive p-0">
            <table class="table table-hover text-nowrap">
              <thead>
                <tr>
                  <th>Nama Barang</th>
                  <th>Stok</th>
                  <th>Min</th>
                  <th>Satuan</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach($low_stock as $item): ?>
                <tr>
                  <td><?php echo $item->nama_barang; ?></td>
                  <td><span class="badge badge-danger"><?php echo $item->stok; ?></span></td>
                  <td><?php echo isset($item->stok_minimum) ? $item->stok_minimum : '10'; ?></td>
                  <td><?php echo $item->nama_satuan; ?></td>
                </tr>
                <?php endforeach; ?>
              </tbody>
            </table>
          </div>
          <!-- /.card-body -->
          <div class="card-footer clearfix">
            <a href="<?php echo site_url('barang'); ?>" class="btn btn-sm btn-danger float-right">Kelola Stok</a>
          </div>
          <!-- /.card-footer -->
        </div>
        <!-- /.card -->
      </section>
      <!-- right col -->
    </div>
    <!-- /.row -->
  </div>
</section>

<script>
$(function() {
  // Transactions Chart
  var ctx1 = document.getElementById('transactions-chart').getContext('2d');
  var transactionsChart = new Chart(ctx1, {
    type: 'line',
    data: {
      labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
      datasets: [{
        label: 'Transaksi Masuk',
        data: [65, 59, 80, 81, 56, 55, 40, 60, 55, 30, 78, 95],
        fill: false,
        borderColor: '#007bff',
        tension: 0.1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });
  
  // Category Chart
  var ctx2 = document.getElementById('category-chart').getContext('2d');
  var categoryChart = new Chart(ctx2, {
    type: 'doughnut',
    data: {
      labels: ['Elektronik', 'Furniture', 'Alat Tulis', 'Lainnya'],
      datasets: [{
        data: [30, 25, 20, 25],
        backgroundColor: ['#007bff', '#28a745', '#ffc107', '#dc3545']
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false
    }
  });
});
</script>
