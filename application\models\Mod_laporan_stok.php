<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * Model Laporan Stok
 * Mengatur berbagai jenis laporan stok
 */
class Mod_laporan_stok extends CI_Model
{
    function __construct()
    {
        parent::__construct();
        $this->load->database();
    }

    /**
     * <PERSON><PERSON><PERSON> Stok Terkini per Barang per Gudang
     */
    function get_laporan_stok_terkini($id_barang = null, $id_gudang = null)
    {
        $this->db->select('
            sb.id_barang,
            sb.id_gudang,
            sb.qty_terakhir,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            b.stok_minimum,
            b.harga_beli,
            b.harga_jual,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan,
            (sb.qty_terakhir * b.harga_beli) as nilai_stok_beli,
            (sb.qty_terakhir * b.harga_jual) as nilai_stok_jual,
            CASE 
                WHEN sb.qty_terakhir <= b.stok_minimum THEN \'Minimum\'
                WHEN sb.qty_terakhir = 0 THEN \'Kosong\'
                ELSE \'Normal\'
            END as status_stok
        ');
        $this->db->from('stok_barang sb');
        $this->db->join('barang b', 'sb.id_barang = b.id');
        $this->db->join('gudang g', 'sb.id_gudang = g.id');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        
        if ($id_barang) {
            $this->db->where('sb.id_barang', $id_barang);
        }
        
        if ($id_gudang) {
            $this->db->where('sb.id_gudang', $id_gudang);
        }
        
        $this->db->where('b.aktif', 1);
        $this->db->where('g.aktif', 1);
        $this->db->order_by('g.nama_gudang, b.nama_barang');
        
        return $this->db->get();
    }

    /**
     * Laporan Movement Stok (Kartu Stok)
     */
    function get_laporan_movement($id_barang = null, $id_gudang = null, $tanggal_dari = null, $tanggal_sampai = null, $tipe_transaksi = null)
    {
        $this->db->select('
            sm.id,
            sm.tanggal,
            sm.id_barang,
            sm.id_gudang,
            sm.tipe_transaksi,
            sm.qty_in,
            sm.qty_out,
            sm.keterangan,
            sm.ref_transaksi,
            sm.user_input,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe as tipe_barang,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan
        ');
        $this->db->from('stok_movement sm');
        $this->db->join('barang b', 'sm.id_barang = b.id');
        $this->db->join('gudang g', 'sm.id_gudang = g.id');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        
        if ($id_barang) {
            $this->db->where('sm.id_barang', $id_barang);
        }
        
        if ($id_gudang) {
            $this->db->where('sm.id_gudang', $id_gudang);
        }
        
        if ($tanggal_dari && $tanggal_sampai) {
            $this->db->where('DATE(sm.tanggal) >=', $tanggal_dari);
            $this->db->where('DATE(sm.tanggal) <=', $tanggal_sampai);
        }
        
        if ($tipe_transaksi) {
            $this->db->where('sm.tipe_transaksi', $tipe_transaksi);
        }
        
        $this->db->order_by('sm.tanggal DESC, sm.id DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Stok Minimum
     */
    function get_laporan_stok_minimum($id_gudang = null)
    {
        $this->db->select('
            sb.id_barang,
            sb.id_gudang,
            sb.qty_terakhir,
            b.kode_barang,
            b.nama_barang,
            b.merk,
            b.tipe,
            b.stok_minimum,
            b.harga_beli,
            b.harga_jual,
            g.kode_gudang,
            g.nama_gudang,
            s.kode_satuan,
            s.nama_satuan,
            (b.stok_minimum - sb.qty_terakhir) as kekurangan
        ');
        $this->db->from('stok_barang sb');
        $this->db->join('barang b', 'sb.id_barang = b.id');
        $this->db->join('gudang g', 'sb.id_gudang = g.id');
        $this->db->join('satuan s', 'b.satuan_id = s.id', 'left');
        
        // Hanya tampilkan yang stoknya <= minimum
        $this->db->where('sb.qty_terakhir <=', 'b.stok_minimum', false);
        
        if ($id_gudang) {
            $this->db->where('sb.id_gudang', $id_gudang);
        }
        
        $this->db->where('b.aktif', 1);
        $this->db->where('g.aktif', 1);
        $this->db->order_by('g.nama_gudang, (b.stok_minimum - sb.qty_terakhir) DESC');
        
        return $this->db->get();
    }

    /**
     * Laporan Nilai Stok per Gudang
     */
    function get_laporan_nilai_stok($id_gudang = null)
    {
        $this->db->select('
            g.id as id_gudang,
            g.kode_gudang,
            g.nama_gudang,
            COUNT(sb.id_barang) as total_item,
            SUM(sb.qty_terakhir) as total_qty,
            SUM(sb.qty_terakhir * b.harga_beli) as total_nilai_beli,
            SUM(sb.qty_terakhir * b.harga_jual) as total_nilai_jual,
            SUM(sb.qty_terakhir * (b.harga_jual - b.harga_beli)) as total_potensi_laba
        ');
        $this->db->from('gudang g');
        $this->db->join('stok_barang sb', 'g.id = sb.id_gudang', 'left');
        $this->db->join('barang b', 'sb.id_barang = b.id', 'left');
        
        if ($id_gudang) {
            $this->db->where('g.id', $id_gudang);
        }
        
        $this->db->where('g.aktif', 1);
        $this->db->where('(b.aktif = 1 OR b.aktif IS NULL)');
        $this->db->group_by('g.id, g.kode_gudang, g.nama_gudang');
        $this->db->order_by('g.nama_gudang');
        
        return $this->db->get();
    }

    /**
     * Get list barang untuk dropdown
     */
    function get_barang_list()
    {
        $this->db->select('id, kode_barang, nama_barang');
        $this->db->from('barang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_barang');
        return $this->db->get();
    }

    /**
     * Get list gudang untuk dropdown
     */
    function get_gudang_list()
    {
        $this->db->select('id, kode_gudang, nama_gudang');
        $this->db->from('gudang');
        $this->db->where('aktif', 1);
        $this->db->order_by('nama_gudang');
        return $this->db->get();
    }

    /**
     * Get summary stok untuk dashboard
     */
    function get_summary_stok()
    {
        $this->db->select('
            COUNT(DISTINCT sb.id_barang) as total_item,
            COUNT(DISTINCT sb.id_gudang) as total_gudang,
            SUM(sb.qty_terakhir) as total_qty,
            SUM(sb.qty_terakhir * b.harga_beli) as total_nilai_beli,
            SUM(sb.qty_terakhir * b.harga_jual) as total_nilai_jual
        ');
        $this->db->from('stok_barang sb');
        $this->db->join('barang b', 'sb.id_barang = b.id');
        $this->db->join('gudang g', 'sb.id_gudang = g.id');
        $this->db->where('b.aktif', 1);
        $this->db->where('g.aktif', 1);
        
        return $this->db->get()->row();
    }

    /**
     * Get barang dengan stok kosong
     */
    function get_barang_stok_kosong($id_gudang = null)
    {
        $this->db->select('
            sb.id_barang,
            sb.id_gudang,
            b.kode_barang,
            b.nama_barang,
            g.kode_gudang,
            g.nama_gudang
        ');
        $this->db->from('stok_barang sb');
        $this->db->join('barang b', 'sb.id_barang = b.id');
        $this->db->join('gudang g', 'sb.id_gudang = g.id');
        $this->db->where('sb.qty_terakhir', 0);
        
        if ($id_gudang) {
            $this->db->where('sb.id_gudang', $id_gudang);
        }
        
        $this->db->where('b.aktif', 1);
        $this->db->where('g.aktif', 1);
        $this->db->order_by('g.nama_gudang, b.nama_barang');
        
        return $this->db->get();
    }

    /**
     * Get tipe transaksi untuk dropdown
     */
    function get_tipe_transaksi()
    {
        return array(
            'pembelian' => 'Pembelian',
            'penjualan' => 'Penjualan',
            'retur_beli' => 'Retur Beli',
            'retur_jual' => 'Retur Jual',
            'penyesuaian' => 'Penyesuaian',
            'opname' => 'Stock Opname',
            'transfer_masuk' => 'Transfer Masuk',
            'transfer_keluar' => 'Transfer Keluar'
        );
    }
}
